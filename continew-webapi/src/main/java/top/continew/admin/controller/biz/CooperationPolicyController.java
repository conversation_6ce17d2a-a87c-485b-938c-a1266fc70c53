package top.continew.admin.controller.biz;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.CooperationPolicyQuery;
import top.continew.admin.biz.model.req.CooperationPolicyReq;
import top.continew.admin.biz.model.resp.CooperationPolicyDetailResp;
import top.continew.admin.biz.model.resp.CooperationPolicyResp;
import top.continew.admin.biz.service.CooperationPolicyService;

/**
 * 渠道合作政策管理 API
 *
 * <AUTHOR>
 * @since 2025/09/08 15:54
 */
@Tag(name = "渠道合作政策管理 API")
@RestController
@CrudRequestMapping(value = "/biz/cooperationPolicy", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class CooperationPolicyController extends BaseController<CooperationPolicyService, CooperationPolicyResp, CooperationPolicyDetailResp, CooperationPolicyQuery, CooperationPolicyReq> {}