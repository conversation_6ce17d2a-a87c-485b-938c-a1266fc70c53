package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import top.continew.admin.biz.model.query.ChannelAdAccountStatQuery;
import top.continew.admin.biz.model.query.ChannelSummaryStatQuery;
import top.continew.admin.biz.model.resp.ChannelAdAccountStatResp;
import top.continew.admin.biz.model.resp.ChannelSummaryStatResp;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.ChannelSpendDailyStatQuery;
import top.continew.admin.biz.model.req.ChannelSpendDailyStatReq;
import top.continew.admin.biz.model.resp.ChannelSpendDailyStatDetailResp;
import top.continew.admin.biz.model.resp.ChannelSpendDailyStatResp;
import top.continew.admin.biz.service.ChannelSpendDailyStatService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.util.List;

/**
 * 每日渠道统计 API
 *
 * <AUTHOR>
 * @since 2025/09/08 15:55
 */
@Tag(name = "每日渠道统计 API")
@RestController
@CrudRequestMapping(value = "/biz/channelSpendDailyStat")
public class ChannelSpendDailyStatController extends BaseController<ChannelSpendDailyStatService, ChannelSpendDailyStatResp, ChannelSpendDailyStatDetailResp, ChannelSpendDailyStatQuery, ChannelSpendDailyStatReq> {


    @GetMapping("/summaryStatPage")
    @Operation(summary = "汇总统计")
    @SaIgnore
    public PageResp<ChannelSummaryStatResp> summaryStatPage(ChannelSummaryStatQuery query, PageQuery pageQuery) {
        return baseService.summaryStatPage(query,pageQuery);
    }

    @GetMapping("/adAccountStatPage")
    @Operation(summary = "广告户统计")
    public PageResp<ChannelAdAccountStatResp> adAccountStatPage(ChannelAdAccountStatQuery query, PageQuery pageQuery) {
        return baseService.adAccountStatPage(query,pageQuery);
    }

}