package top.continew.admin.job;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.service.ChannelSpendDailyStatService;

import java.time.LocalDateTime;
import java.util.List;

@Component
@RequiredArgsConstructor
public class ChannelJob {

    private final ChannelSpendDailyStatService channelSpendDailyStatService;

    @JobExecutor(name = "syncChannelSpendDailyStatData")
    public void syncChannelSpendDailyStatData(JobArgs jobArgs) {
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;

        if (jobArgs.getJobParams() != null) {
            String paramsStr = (String) jobArgs.getJobParams();
            List<String> paramsSplit = StrUtil.split(paramsStr, "|", true, true);
            if (!paramsSplit.isEmpty()) {
                if (paramsSplit.size() > 1) {
                    startTime = LocalDateTimeUtil.parseDate(paramsSplit.get(1), "yyyy-MM-dd").atTime(0, 0, 0);
                    endTime = LocalDateTimeUtil.parseDate(paramsSplit.get(2), "yyyy-MM-dd").atTime(23, 59, 59);
                }
            }
        }

        LocalDateTime now = LocalDateTime.now();
        if (startTime == null) {
            endTime = now.toLocalDate().atTime(23, 59, 59);
            startTime = now.minusDays(7).toLocalDate().atTime(0, 0, 0);
        }


        channelSpendDailyStatService.syncData(startTime, endTime);
    }

}
