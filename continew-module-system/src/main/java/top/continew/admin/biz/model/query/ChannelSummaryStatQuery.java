package top.continew.admin.biz.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ChannelSummaryStatQuery {

    @Schema(description = "渠道ID列表")
    private List<Long> channelIds;

    @Schema(description = "BM类型列表")
    private List<Long> bmTypes;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;
}
