package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 渠道合作政策信息
 *
 * <AUTHOR>
 * @since 2025/09/08 15:54
 */
@Data
@Schema(description = "渠道合作政策信息")
public class CooperationPolicyResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道ID
     */
    @Schema(description = "渠道ID")
    private Long channelId;

    /**
     * BM类型
     */
    @Schema(description = "BM类型")
    private Long bmType;

    /**
     * 开户费，两位小数
     */
    @Schema(description = "开户费，两位小数")
    private BigDecimal accountOpenFee;

    /**
     * 服务费率，百分比，两位小数
     */
    @Schema(description = "服务费率，百分比，两位小数")
    private BigDecimal serviceFeeRatePercent;

    /**
     * 消耗返押金的达标金额，金额，两位小数
     */
    @Schema(description = "消耗返押金的达标金额，金额，两位小数")
    private BigDecimal depositReturnThresholdAmount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "BM坑位类型")
    private String typeName;

    @Schema(description = "BM渠道")
    private String businessManagerChannel;
}