package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 每日渠道统计信息
 *
 * <AUTHOR>
 * @since 2025/09/08 15:55
 */
@Data
@Schema(description = "每日渠道统计信息")
public class ChannelSpendDailyStatResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    private LocalDate statDate;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    private String channelName;

    /**
     * BM类型
     */
    @Schema(description = "BM类型")
    private Integer bmType;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private Long customerId;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    private String adAccountId;

    /**
     * 消耗金额
     */
    @Schema(description = "消耗金额")
    private BigDecimal spendAmount;

    /**
     * 客户服务费率
     */
    @Schema(description = "客户服务费率")
    private BigDecimal customerServiceRate;

    /**
     * 渠道服务费率
     */
    @Schema(description = "渠道服务费率")
    private BigDecimal channelServiceRate;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}