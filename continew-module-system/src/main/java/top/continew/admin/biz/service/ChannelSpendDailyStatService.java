package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.ChannelSpendDailyStatDO;
import top.continew.admin.biz.model.query.ChannelAdAccountStatQuery;
import top.continew.admin.biz.model.query.ChannelSpendDailyStatQuery;
import top.continew.admin.biz.model.query.ChannelSummaryStatQuery;
import top.continew.admin.biz.model.req.ChannelSpendDailyStatReq;
import top.continew.admin.biz.model.resp.ChannelAdAccountStatResp;
import top.continew.admin.biz.model.resp.ChannelSpendDailyStatDetailResp;
import top.continew.admin.biz.model.resp.ChannelSpendDailyStatResp;
import top.continew.admin.biz.model.resp.ChannelSummaryStatResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 每日渠道统计业务接口
 *
 * <AUTHOR>
 * @since 2025/09/08 15:55
 */
public interface ChannelSpendDailyStatService extends BaseService<ChannelSpendDailyStatResp, ChannelSpendDailyStatDetailResp, ChannelSpendDailyStatQuery, ChannelSpendDailyStatReq>, IService<ChannelSpendDailyStatDO> {

    void syncData(LocalDateTime startDate, LocalDateTime endDate);

    PageResp<ChannelSummaryStatResp> summaryStatPage(ChannelSummaryStatQuery query, PageQuery pageQuery);

    PageResp<ChannelAdAccountStatResp> adAccountStatPage(ChannelAdAccountStatQuery query,PageQuery pageQuery);
}