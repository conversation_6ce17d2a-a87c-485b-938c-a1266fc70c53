package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 每日渠道统计详情信息
 *
 * <AUTHOR>
 * @since 2025/09/08 15:55
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "渠道合作政策详情信息")
public class ChannelSpendDailyStatDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    @ExcelProperty(value = "统计日期")
    private LocalDate statDate;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    @ExcelProperty(value = "渠道名称")
    private String channelName;

    /**
     * BM类型
     */
    @Schema(description = "BM类型")
    @ExcelProperty(value = "BM类型")
    private Integer bmType;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @ExcelProperty(value = "广告户ID")
    private String adAccountId;

    /**
     * 消耗金额
     */
    @Schema(description = "消耗金额")
    @ExcelProperty(value = "消耗金额")
    private BigDecimal spendAmount;

    /**
     * 客户服务费率
     */
    @Schema(description = "客户服务费率")
    @ExcelProperty(value = "客户服务费率")
    private BigDecimal customerServiceRate;

    /**
     * 渠道服务费率
     */
    @Schema(description = "渠道服务费率")
    @ExcelProperty(value = "渠道服务费率")
    private BigDecimal channelServiceRate;
}