package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改每日渠道统计参数
 *
 * <AUTHOR>
 * @since 2025/09/08 15:55
 */
@Data
@Schema(description = "创建或修改渠道合作政策参数")
public class ChannelSpendDailyStatReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    @NotNull(message = "统计日期不能为空")
    private LocalDate statDate;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    @NotBlank(message = "渠道名称不能为空")
    @Length(max = 100, message = "渠道名称长度不能超过 {max} 个字符")
    private String channelName;

    /**
     * BM类型
     */
    @Schema(description = "BM类型")
    @NotNull(message = "BM类型不能为空")
    private Integer bmType;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @NotBlank(message = "广告户ID不能为空")
    @Length(max = 100, message = "广告户ID长度不能超过 {max} 个字符")
    private String adAccountId;

    /**
     * 消耗金额
     */
    @Schema(description = "消耗金额")
    @NotNull(message = "消耗金额不能为空")
    private BigDecimal spendAmount;

    /**
     * 客户服务费率
     */
    @Schema(description = "客户服务费率")
    @NotNull(message = "客户服务费率不能为空")
    private BigDecimal customerServiceRate;

    /**
     * 渠道服务费率
     */
    @Schema(description = "渠道服务费率")
    @NotNull(message = "渠道服务费率不能为空")
    private BigDecimal channelServiceRate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;
}