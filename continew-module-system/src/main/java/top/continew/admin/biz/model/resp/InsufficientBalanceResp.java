package top.continew.admin.biz.model.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class InsufficientBalanceResp {

    private String adAccountId;

    private Long bmType;

    private String browserNo;

    private BigDecimal cardBalance;

    private BigDecimal cardSpent;

    private String customerName;

    private Long customerId;

    private LocalDateTime finishTime;

    private String timezone;

    private BigDecimal rechargeAmount;

    private LocalDateTime rechargeTime;

    private BigDecimal spentAmount;

    private LocalDate spentTime;

}
