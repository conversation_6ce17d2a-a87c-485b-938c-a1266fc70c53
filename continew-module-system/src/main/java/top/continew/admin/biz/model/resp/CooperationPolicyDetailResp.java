package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelIgnore;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 渠道合作政策详情信息
 *
 * <AUTHOR>
 * @since 2025/09/08 15:54
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "渠道合作政策详情信息")
public class CooperationPolicyDetailResp extends BaseDetailResp{

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道ID
     */
    @Schema(description = "渠道ID")
    @ExcelProperty(value = "渠道ID")
    @ExcelIgnore
    private Long channelId;

    @ExcelProperty(value = "BM坑位类型")
    private String typeName;

    @ExcelProperty(value = "BM渠道")
    private String businessManagerChannel;
    /**
     * BM类型
     */
    @Schema(description = "BM类型")
    @ExcelIgnore
    @ExcelProperty(value = "BM类型")
    private Long bmType;

    /**
     * 开户费，两位小数
     */
    @Schema(description = "开户费，两位小数")
    @ExcelProperty(value = "开户费")
    private BigDecimal accountOpenFee;

    /**
     * 服务费率，百分比，两位小数
     */
    @Schema(description = "服务费率，百分比，两位小数")
    @ExcelProperty(value = "服务费率")
    private BigDecimal serviceFeeRatePercent;

    /**
     * 消耗返押金的达标金额，金额，两位小数
     */
    @Schema(description = "消耗返押金的达标金额，金额，两位小数")
    @ExcelProperty(value = "消耗返押金的达标金额")
    private BigDecimal depositReturnThresholdAmount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;



}