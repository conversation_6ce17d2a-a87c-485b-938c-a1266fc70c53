package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class ChannelSummaryStatResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "统计日期", example = "2023-10-26")
    private LocalDate statDate;

    @Schema(description = "渠道名称", example = "Google Ads")
    private String channelName;

    @Schema(description = "BM类型", example = "电商广告")
    private String bmType;

    @Schema(description = "总消耗金额", example = "10000.55")
    private BigDecimal totalSpendAmount;

    @Schema(description = "渠道支出 - 服务费", example = "200.10")
    private BigDecimal channelServiceFeeExpense;

    @Schema(description = "渠道支出 - 开户费", example = "50.00")
    private BigDecimal channelAccountOpeningFeeExpense;

    @Schema(description = "客户收入 - 服务费", example = "300.20")
    private BigDecimal customerServiceFeeIncome;

    @Schema(description = "客户收入 - 开户费", example = "75.00")
    private BigDecimal customerAccountOpeningFeeIncome;

    @Schema(description = "利润", example = "125.10")
    private BigDecimal profit;
}