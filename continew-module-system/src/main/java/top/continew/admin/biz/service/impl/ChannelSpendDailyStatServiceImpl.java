package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.model.query.ChannelAdAccountStatQuery;
import top.continew.admin.biz.model.query.ChannelSummaryStatQuery;
import top.continew.admin.biz.model.resp.ChannelAdAccountStatResp;
import top.continew.admin.biz.model.resp.ChannelSummaryStatResp;
import top.continew.admin.biz.service.*;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.ChannelSpendDailyStatMapper;
import top.continew.admin.biz.model.query.ChannelSpendDailyStatQuery;
import top.continew.admin.biz.model.req.ChannelSpendDailyStatReq;
import top.continew.admin.biz.model.resp.ChannelSpendDailyStatDetailResp;
import top.continew.admin.biz.model.resp.ChannelSpendDailyStatResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 每日渠道统计业务实现
 *
 * <AUTHOR>
 * @since 2025/09/08 15:55
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ChannelSpendDailyStatServiceImpl extends BaseServiceImpl<ChannelSpendDailyStatMapper, ChannelSpendDailyStatDO, ChannelSpendDailyStatResp, ChannelSpendDailyStatDetailResp, ChannelSpendDailyStatQuery, ChannelSpendDailyStatReq> implements ChannelSpendDailyStatService {

    private final CooperationPolicyService cooperationPolicyService;

    private final AdAccountService adAccountService;

    private final AdAccountInsightService adAccountInsightService;

    private final CustomerService customerService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncData(LocalDateTime startDate, LocalDateTime endDate) {
        log.info("开始同步每日渠道统计数据，开始日期：{}，结束日期：{}", startDate, endDate);

        List<ChannelSpendDailyStatDO> saveList = new ArrayList<>();

        // 合作策略所有数据
        List<CooperationPolicyDO> cooperationPolicyList = cooperationPolicyService.list();
        if (CollUtil.isEmpty(cooperationPolicyList)) {
            log.warn("未找到合作政策数据，跳过同步");
            return;
        }

        // 创建合作政策服务费率映射 Map<channelId_bmType, serviceFeeRatePercent>
        Map<String, BigDecimal> policyServiceRateMap = cooperationPolicyList.stream()
                .collect(Collectors.toMap(
                        policy -> policy.getChannelId() + "_" + policy.getBmType(),
                        CooperationPolicyDO::getServiceFeeRatePercent
                ));

        // 广告户所有数据
        List<AdAccountDO> adAccountList = new ArrayList<>();

        for (CooperationPolicyDO cooperationPolicyDO : cooperationPolicyList) {
            List<AdAccountDO> adAccountListByPolicy = adAccountService.list(Wrappers.<AdAccountDO>lambdaQuery()
                    .eq(AdAccountDO::getBmItemChannelId, cooperationPolicyDO.getChannelId())
                    .eq(AdAccountDO::getBmItemType, cooperationPolicyDO.getBmType()));
            adAccountList.addAll(adAccountListByPolicy);
        }

        if (CollUtil.isEmpty(adAccountList)) {
            log.warn("未找到广告户数据，跳过同步");
            return;
        }

        // 广告户每日消耗Map
        Map<String, List<AdAccountInsightDO>> adAccountInsightMap = adAccountInsightService.lambdaQuery()
                .between(AdAccountInsightDO::getStatDate, startDate, endDate)
                .list().stream().collect(Collectors.groupingBy(AdAccountInsightDO::getAdAccountId));


        Set<Long> customerIds = adAccountInsightMap.values().stream()
                .flatMap(List::stream)
                .map(AdAccountInsightDO::getCustomerId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<Long, CustomerDO> customerMap = Collections.emptyMap();
        if (!customerIds.isEmpty()) {
            customerMap = customerService.listByIds(customerIds)
                    .stream()
                    .collect(Collectors.toMap(CustomerDO::getId, Function.identity()));
        }

        for (AdAccountDO adAccountDO : adAccountList) {
            List<AdAccountInsightDO> accountInsightList = adAccountInsightMap.getOrDefault(adAccountDO.getPlatformAdId(), null);
            if (CollUtil.isEmpty(accountInsightList)) {
                continue;
            }
            for (AdAccountInsightDO adAccountInsightDO : accountInsightList) {
                ChannelSpendDailyStatDO channelSpendDailyStatDO = new ChannelSpendDailyStatDO();
                channelSpendDailyStatDO.setStatDate(adAccountInsightDO.getStatDate());
                channelSpendDailyStatDO.setChannelId(adAccountDO.getBmItemChannelId());
                channelSpendDailyStatDO.setBmType(adAccountDO.getBmItemType());
                channelSpendDailyStatDO.setAdAccountId(adAccountDO.getPlatformAdId());
                channelSpendDailyStatDO.setSpendAmount(adAccountInsightDO.getSpend());

                // 设置客户ID和客户服务费率
                if (adAccountInsightDO.getCustomerId() != null) {
                    channelSpendDailyStatDO.setCustomerId(adAccountInsightDO.getCustomerId());
                    CustomerDO customer = customerMap.get(adAccountInsightDO.getCustomerId());
                    if (customer != null) {
                        channelSpendDailyStatDO.setCustomerServiceRate(customer.getFeeRatePercent());
                    } else {
                        log.warn("未找到客户ID: {} 的信息", adAccountInsightDO.getCustomerId());
                        channelSpendDailyStatDO.setCustomerServiceRate(BigDecimal.ZERO);
                    }
                }

                // 设置渠道服务费率（来自合作政策）
                String policyKey = adAccountDO.getBmItemChannelId() + "_" + adAccountDO.getBmItemType();
                BigDecimal channelServiceRate = policyServiceRateMap.get(policyKey);
                if (channelServiceRate != null) {
                    channelSpendDailyStatDO.setChannelServiceRate(channelServiceRate);
                } else {
                    log.warn("未找到渠道ID: {} 和BM类型: {} 对应的合作政策服务费率",
                            adAccountDO.getBmItemChannelId(), adAccountDO.getBmItemType());
                    channelSpendDailyStatDO.setChannelServiceRate(BigDecimal.ZERO);
                }

                saveList.add(channelSpendDailyStatDO);
            }
        }

        // 精确删除相关渠道和BM类型的旧数据，避免唯一约束冲突
        for (CooperationPolicyDO policy : cooperationPolicyList) {
            int deletedCount = baseMapper.delete(Wrappers.<ChannelSpendDailyStatDO>lambdaQuery()
                    .between(ChannelSpendDailyStatDO::getStatDate, startDate.toLocalDate(), endDate.toLocalDate())
                    .eq(ChannelSpendDailyStatDO::getChannelId, policy.getChannelId())
                    .eq(ChannelSpendDailyStatDO::getBmType, policy.getBmType()));
            if (deletedCount > 0) {
                log.info("删除渠道ID: {} BM类型: {} 的旧数据 {} 条",
                        policy.getChannelId(), policy.getBmType(), deletedCount);
            }
        }

        // 插入新的统计数据
        if (!saveList.isEmpty()) {
            saveBatch(saveList);
        }
        log.info("完成同步每日渠道统计数据，共处理 {} 条记录", saveList.size());
    }

    @Override
    public PageResp<ChannelSummaryStatResp> summaryStatPage(ChannelSummaryStatQuery query, PageQuery pageQuery) {
        IPage<ChannelSummaryStatResp> page = baseMapper.selectSummaryStatPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), query);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public PageResp<ChannelAdAccountStatResp> adAccountStatPage(ChannelAdAccountStatQuery query, PageQuery pageQuery) {
        IPage<ChannelAdAccountStatResp> page = baseMapper.selectAdAccountStatPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), query);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }


}