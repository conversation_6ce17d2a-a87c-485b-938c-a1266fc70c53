package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.resp.CooperationPolicyResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.CooperationPolicyDO;

/**
* 渠道合作政策 Mapper
*
* <AUTHOR>
* @since 2025/09/08 15:54
*/
public interface CooperationPolicyMapper extends BaseMapper<CooperationPolicyDO> {
    IPage<CooperationPolicyResp> selectCustomPage(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<CooperationPolicyDO> wrapper);
}