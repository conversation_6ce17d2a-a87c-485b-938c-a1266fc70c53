package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 每日渠道统计查询条件
 *
 * <AUTHOR>
 * @since 2025/09/08 15:55
 */
@Data
@Schema(description = "渠道合作政策查询条件")
public class ChannelSpendDailyStatQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    @Query(type = QueryType.EQ)
    private LocalDate statDate;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    @Query(type = QueryType.EQ)
    private String channelName;

    /**
     * BM类型
     */
    @Schema(description = "BM类型")
    @Query(type = QueryType.EQ)
    private Integer bmType;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @Query(type = QueryType.EQ)
    private String adAccountId;

    /**
     * 消耗金额
     */
    @Schema(description = "消耗金额")
    @Query(type = QueryType.EQ)
    private BigDecimal spendAmount;

    /**
     * 客户服务费率
     */
    @Schema(description = "客户服务费率")
    @Query(type = QueryType.EQ)
    private BigDecimal customerServiceRate;

    /**
     * 渠道服务费率
     */
    @Schema(description = "渠道服务费率")
    @Query(type = QueryType.EQ)
    private BigDecimal channelServiceRate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.EQ)
    private LocalDateTime createTime;
}