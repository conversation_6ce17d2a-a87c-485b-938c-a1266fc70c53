package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 每日渠道统计实体
 *
 * <AUTHOR>
 * @since 2025/09/08 15:55
 */
@Data
@TableName("biz_channel_spend_daily_stat")
public class ChannelSpendDailyStatDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDate statDate;

    /**
     * 渠道名称
     */
    private Long channelId;

    /**
     * BM类型
     */
    private Long bmType;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 广告户ID
     */
    private String adAccountId;

    /**
     * 消耗金额
     */
    private BigDecimal spendAmount;

    /**
     * 客户服务费率
     */
    private BigDecimal customerServiceRate;

    /**
     * 渠道服务费率
     */
    private BigDecimal channelServiceRate;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}