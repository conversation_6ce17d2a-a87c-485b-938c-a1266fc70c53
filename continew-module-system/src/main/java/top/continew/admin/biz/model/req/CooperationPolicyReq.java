package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改渠道合作政策参数
 *
 * <AUTHOR>
 * @since 2025/09/08 15:54
 */
@Data
@Schema(description = "创建或修改渠道合作政策参数")
public class CooperationPolicyReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道ID
     */
    @Schema(description = "渠道ID")
    @NotNull(message = "渠道ID不能为空")
    private Long channelId;

    /**
     * BM类型
     */
    @Schema(description = "BM类型")
    @NotNull(message = "BM类型不能为空")
    private Long bmType;

    /**
     * 开户费，两位小数
     */
    @Schema(description = "开户费，两位小数")
    @NotNull(message = "开户费，两位小数不能为空")
    private BigDecimal accountOpenFee;

    /**
     * 服务费率，百分比，两位小数
     */
    @Schema(description = "服务费率，百分比，两位小数")
    @NotNull(message = "服务费率，百分比，两位小数不能为空")
    private BigDecimal serviceFeeRatePercent;

    /**
     * 消耗返押金的达标金额，金额，两位小数
     */
    @Schema(description = "消耗返押金的达标金额，金额，两位小数")
    private BigDecimal depositReturnThresholdAmount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 255, message = "备注长度不能超过 {max} 个字符")
    private String remark;


}