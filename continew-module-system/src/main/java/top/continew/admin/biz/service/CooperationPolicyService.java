package top.continew.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import top.continew.admin.biz.model.entity.CooperationPolicyDO;
import top.continew.admin.biz.model.query.CooperationPolicyQuery;
import top.continew.admin.biz.model.req.CooperationPolicyReq;
import top.continew.admin.biz.model.resp.CooperationPolicyDetailResp;
import top.continew.admin.biz.model.resp.CooperationPolicyResp;
import top.continew.starter.extension.crud.service.BaseService;

/**
 * customer
 * 渠道合作政策业务接口
 *
 * <AUTHOR>
 * @since 2025/09/08 15:54
 */
public interface CooperationPolicyService extends BaseService<CooperationPolicyResp, CooperationPolicyDetailResp, CooperationPolicyQuery, CooperationPolicyReq>, IService<CooperationPolicyDO> {
}