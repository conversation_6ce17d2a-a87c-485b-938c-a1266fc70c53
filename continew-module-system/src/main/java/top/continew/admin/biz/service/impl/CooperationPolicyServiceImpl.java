package top.continew.admin.biz.service.impl;

import cn.hutool.core.exceptions.CheckedUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;


import top.continew.admin.biz.model.entity.BusinessManagerChannelDO;
import top.continew.admin.biz.model.entity.BusinessManagerDO;
import top.continew.admin.biz.model.entity.ProfitTypeDO;
import top.continew.admin.biz.service.*;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.CooperationPolicyMapper;
import top.continew.admin.biz.model.entity.CooperationPolicyDO;
import top.continew.admin.biz.model.query.CooperationPolicyQuery;
import top.continew.admin.biz.model.req.CooperationPolicyReq;
import top.continew.admin.biz.model.resp.CooperationPolicyDetailResp;
import top.continew.admin.biz.model.resp.CooperationPolicyResp;
import top.continew.starter.file.excel.util.ExcelUtils;

import java.util.List;

/**
 * 渠道合作政策业务实现
 *
 * <AUTHOR>
 * @since 2025/09/08 15:54
 */
@Service
@RequiredArgsConstructor
public class CooperationPolicyServiceImpl extends BaseServiceImpl<CooperationPolicyMapper, CooperationPolicyDO, CooperationPolicyResp, CooperationPolicyDetailResp, CooperationPolicyQuery, CooperationPolicyReq> implements CooperationPolicyService {

    private final BusinessManagerChannelService businessManagerChannelService;

    private final ProfitTypeService profitTypeService;

    @Override
    public PageResp<CooperationPolicyResp> page(CooperationPolicyQuery query, PageQuery pageQuery) {
        QueryWrapper<CooperationPolicyDO> wrapper = buildQueryWrapper(query);
        IPage<CooperationPolicyResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), wrapper);
        return PageResp.build(page);
    }

    @Override
    protected void beforeAdd(CooperationPolicyReq req) {
        checkValid(req,null);
        super.beforeAdd(req);
    }

    @Override
    protected void beforeUpdate(CooperationPolicyReq req, Long id) {
        checkValid(req,id);
        super.beforeUpdate(req, id);
    }

    //
    public void checkValid(CooperationPolicyReq req, Long id){
        //校验是否存在相同的渠道 bm类型
        CooperationPolicyDO channel = this.getOne(Wrappers.<CooperationPolicyDO>lambdaQuery()
                .eq(CooperationPolicyDO::getChannelId, req.getChannelId())
                .eq(CooperationPolicyDO::getBmType, req.getBmType())
                .ne(id != null ,CooperationPolicyDO::getId, id));
        CheckUtils.throwIfNotNull(channel,"该渠道 bm类型已存在");
    }


    @Override
    public void export(CooperationPolicyQuery query, SortQuery sortQuery, HttpServletResponse response) {
        List<CooperationPolicyDetailResp> list = this.list(query, sortQuery, this.getDetailClass());
        list.forEach(item -> {
            BusinessManagerChannelDO channel = businessManagerChannelService
                    .getOne(Wrappers.<BusinessManagerChannelDO>lambdaQuery()
                            .eq(BusinessManagerChannelDO::getId, item.getChannelId()));
            item.setBusinessManagerChannel(channel == null ? "":channel.getName());
            ProfitTypeDO profit = profitTypeService.getOne(Wrappers.<ProfitTypeDO>lambdaQuery().eq(ProfitTypeDO::getId, item.getBmType()));
            item.setTypeName(profit == null ? "" : profit.getName());
        });
        ExcelUtils.export(list, "导出数据", this.getDetailClass(), response);
    }
}