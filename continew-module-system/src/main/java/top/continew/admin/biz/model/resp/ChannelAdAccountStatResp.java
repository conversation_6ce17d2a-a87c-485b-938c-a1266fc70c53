package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.AdAccountStatusEnum;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "渠道广告户统计响应实体")
public class ChannelAdAccountStatResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "渠道名称")
    private String channelName;

    @Schema(description = "BM类型")
    private Long bmType;

    @Schema(description = "拿户时间")
    private LocalDateTime accountAcquisitionTime;

    @Schema(description = "广告户ID")
    private String adAccountId;

    @Schema(description = "广告户状态")
    private AdAccountStatusEnum adAccountStatus;

    @Schema(description = "总消耗金额")
    private BigDecimal totalSpendAmount;
}