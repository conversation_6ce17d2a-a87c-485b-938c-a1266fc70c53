<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.ChannelSpendDailyStatMapper">
    <select id="selectSummaryStatPage" resultType="top.continew.admin.biz.model.resp.ChannelSummaryStatResp">
        select bbmc.name       as channelName,
               bcsds.stat_date as statDate,
               bpt.name        as bmType
        from biz_channel_spend_daily_stat bcsds
                 left join biz_business_manager_channel bbmc on bbmc.id = bcsds.channel_id
                 left join biz_profit_type bpt on bpt.id = bcsds.bm_type
    </select>
    <select id="selectAdAccountStatPage"
            resultType="top.continew.admin.biz.model.resp.ChannelAdAccountStatResp">

    </select>
</mapper>