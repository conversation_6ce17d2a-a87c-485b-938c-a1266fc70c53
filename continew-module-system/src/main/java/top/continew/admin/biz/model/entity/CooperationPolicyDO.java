package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 渠道合作政策实体
 *
 * <AUTHOR>
 * @since 2025/09/08 15:54
 */
@Data
@TableName("biz_cooperation_policy")
public class CooperationPolicyDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * BM类型
     */
    private Long bmType;

    /**
     * 开户费，两位小数
     */
    private BigDecimal accountOpenFee;

    /**
     * 服务费率，百分比，两位小数
     */
    private BigDecimal serviceFeeRatePercent;

    /**
     * 消耗返押金的达标金额，金额，两位小数
     */
    private BigDecimal depositReturnThresholdAmount;

    /**
     * 备注
     */
    private String remark;

}