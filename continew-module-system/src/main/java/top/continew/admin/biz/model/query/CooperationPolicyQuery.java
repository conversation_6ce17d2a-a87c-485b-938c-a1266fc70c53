package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 渠道合作政策查询条件
 *
 * <AUTHOR>
 * @since 2025/09/08 15:54
 */
@Data
@Schema(description = "渠道合作政策查询条件")
public class CooperationPolicyQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道ID
     */
    @Schema(description = "渠道ID")
    @Query(type = QueryType.EQ)
    private Long channelId;

    /**
     * BM类型
     */
    @Schema(description = "BM类型")
    @Query(type = QueryType.EQ)
    private Long bmType;

}