<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.ChannelSpendDailyStatMapper">
    <select id="selectSummaryStatPage" resultType="top.continew.admin.biz.model.resp.ChannelSummaryStatResp">
        WITH openAccount AS (SELECT platform_ad_id  AS adAccountId,
                                    type            AS bmType,
                                    channel_id      AS channelId,
                                    SUM(unit_price) AS totalUnitPrice
                             FROM biz_business_manager_item
                             GROUP BY platform_ad_id,
                                      type,
                                      channel_id),
             customerOpeningFeePerChannelBm AS (SELECT bmi.channel_id      AS channelId,
                                                       bmi.type            AS bmType,
                                                       SUM(bao.pay_amount) AS totalCustomerOpeningFee
                                                FROM biz_ad_account_order bao
                                                         JOIN biz_business_manager_item bmi
                                                    -- 通过 ad_account_id 关联广告户订单和BM坑位，获取渠道ID和BM类型
                                                              ON bao.ad_account_id = bmi.platform_ad_id
                                                -- 可以根据需要添加过滤条件，例如订单状态为完成的、未退款的等等
                                                -- WHERE bao.status = 1 AND bao.refunded = 0
                                                GROUP BY bmi.channel_id,
                                                         bmi.type)
        SELECT bbmc.NAME                                             AS channelName,
               bpt.NAME                                              AS bmType,
               sum(bcsds.spend_amount)                               AS totalSpendAmount,
               sum(bcsds.spend_amount * bcsds.channel_service_rate)  AS channelServiceFeeExpense,
               COALESCE(
                       (SELECT SUM(oa_sub.totalUnitPrice)
                        FROM openAccount oa_sub
                        WHERE oa_sub.channelId = bcsds.channel_id
                          AND oa_sub.bmType = bcsds.bm_type),
                       0
               )                                                     AS channelAccountOpeningFeeExpense,
               sum(bcsds.spend_amount * bcsds.customer_service_rate) AS customerServiceFeeIncome,
               -- 客户收入-开户费
               COALESCE(cof_per_cb.totalCustomerOpeningFee, 0) AS customerAccountOpeningFeeIncome
        FROM biz_channel_spend_daily_stat bcsds
                 LEFT JOIN biz_business_manager_channel bbmc ON bbmc.id = bcsds.channel_id
                 LEFT JOIN biz_profit_type bpt ON bpt.id = bcsds.bm_type
                 LEFT JOIN customerOpeningFeePerChannelBm cof_per_cb
                           ON cof_per_cb.channelId = bcsds.channel_id
                               AND cof_per_cb.bmType = bcsds.bm_type
        GROUP BY bcsds.channel_id,
                 bcsds.bm_type
        ORDER BY bcsds.channel_id, bcsds.bm_type
    </select>
    <select id="selectAdAccountStatPage"
            resultType="top.continew.admin.biz.model.resp.ChannelAdAccountStatResp">

    </select>
</mapper>
