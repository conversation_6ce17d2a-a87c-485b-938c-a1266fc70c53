<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CooperationPolicyMapper">
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.CooperationPolicyResp">
        select bcp.id, bcp.channel_id, bcp.bm_type, bcp.account_open_fee, bcp.service_fee_rate_percent,
               bcp.deposit_return_threshold_amount, bcp.remark, pt.name as typeName, bbmc.name as businessManagerChannel
        from biz_cooperation_policy bcp
            left join biz_profit_type pt on pt.id = bcp.bm_type
            left join biz_business_manager_channel bbmc ON bcp.channel_id = bbmc.id
            ${ew.customSqlSegment}
    </select>
</mapper>